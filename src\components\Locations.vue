<script setup>
import { ref, computed } from 'vue'
import Map from './Map.vue'

// Location data for Kenema Pharmacies
const locations = ref([
  {
    id: 1,
    name: "Kiz Bole Bulbula",
    address: "Street 6110-00, Tamrat Centre",
    phone: "+251911234567",
    email: "<EMAIL>",
    latitude: 8.9806,
    longitude: 38.7578,
    isOpen: true,
    hours: "8:00 AM - 10:00 PM"
  },
  {
    id: 2,
    name: "Kiz Gerji Jackros Street",
    address: "Street 6, Gerji 100 Jackros Centre",
    phone: "+251911234568", 
    email: "<EMAIL>",
    latitude: 9.0579,
    longitude: 38.7199,
    isOpen: true,
    hours: "8:00 AM - 10:00 PM"
  },
  {
    id: 3,
    name: "Kenema Pharmacy, Number 14",
    address: "Addis Ababa",
    phone: "+0932354518",
    email: "<EMAIL>", 
    latitude: 9.059569686802346,
    longitude: 38.71989604398679,
    isOpen: true,
    hours: "8:00 AM - 10:00 PM"
  }
])

const selectedLocation = ref(null)
const searchQuery = ref('')

// Filter locations based on search query
const filteredLocations = computed(() => {
  if (!searchQuery.value) return locations.value
  
  return locations.value.filter(location => 
    location.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    location.address.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// Get center coordinates for map (average of all locations)
const mapCenter = computed(() => {
  if (selectedLocation.value) {
    return {
      latitude: selectedLocation.value.latitude,
      longitude: selectedLocation.value.longitude
    }
  }
  
  const avgLat = locations.value.reduce((sum, loc) => sum + loc.latitude, 0) / locations.value.length
  const avgLng = locations.value.reduce((sum, loc) => sum + loc.longitude, 0) / locations.value.length
  
  return {
    latitude: avgLat,
    longitude: avgLng
  }
})

const selectLocation = (location) => {
  selectedLocation.value = location
}

const clearSelection = () => {
  selectedLocation.value = null
}

const navigateToLocation = (location) => {
  const url = `https://www.google.com/maps/dir/?api=1&destination=${location.latitude},${location.longitude}`
  window.open(url, '_blank')
}
</script>

<template>
  <div id="Locations" class="bg-[#FFFCFA] w-full py-20">
    <div class="max-w-[1296px] mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-[#363940] mb-4">Our Locations</h2>
        <p class="text-[#666666] text-lg max-w-2xl mx-auto">
          Find the nearest Kenema Pharmacy location to you. We're committed to serving communities across Addis Ababa.
        </p>
      </div>

      <!-- Search Bar -->
      <div class="mb-8">
        <div class="relative max-w-md mx-auto">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search anything..."
            class="w-full px-4 py-3 pl-10 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div class="bg-orange-500 rounded-full p-2">
              <span class="text-white text-sm font-bold">B</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Locations List -->
      <div class="max-w-md mx-auto space-y-3">
        <div
          v-for="(location, index) in filteredLocations"
          :key="location.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          :class="index === 0 ? 'bg-orange-500 text-white' : 'hover:shadow-md'"
          @click="selectLocation(location)"
        >
          <div class="p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                  :class="index === 0 ? 'bg-white text-orange-500' : 'bg-orange-500 text-white'"
                >
                  {{ index + 1 }}
                </div>
                <div>
                  <h3
                    class="font-semibold text-sm"
                    :class="index === 0 ? 'text-white' : 'text-gray-900'"
                  >
                    {{ location.name }}
                  </h3>
                  <p
                    class="text-xs"
                    :class="index === 0 ? 'text-orange-100' : 'text-gray-500'"
                  >
                    {{ location.address }}
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-xs">|</span>
                <button
                  @click.stop="navigateToLocation(location)"
                  class="text-xs font-medium"
                  :class="index === 0 ? 'text-white' : 'text-orange-500'"
                >
                  Map
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- No results message -->
        <div v-if="filteredLocations.length === 0" class="text-center py-8">
          <p class="text-gray-500 text-sm">No locations found matching your search.</p>
        </div>
      </div>

      <!-- Map Section (below the list) -->
      <div class="mt-12 bg-white rounded-lg shadow-lg overflow-hidden max-w-4xl mx-auto">
        <div class="p-4 bg-orange-500 text-white">
          <h3 class="text-xl font-semibold">
            {{ selectedLocation ? selectedLocation.name : 'All Locations' }}
          </h3>
          <button
            v-if="selectedLocation"
            @click="clearSelection"
            class="mt-2 text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded"
          >
            Show All Locations
          </button>
        </div>
        <div class="h-96">
          <Map
            :latitude="mapCenter.latitude"
            :longitude="mapCenter.longitude"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("https://fonts.googleapis.com/css?family=Ubuntu");

h2, h3 {
  font-family: "Ubuntu", sans-serif;
}

p, span {
  font-family: "Ubuntu", sans-serif;
}
</style>
