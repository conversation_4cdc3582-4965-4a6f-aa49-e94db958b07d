<script setup>
import { ref, computed } from 'vue'

const currentSlide = ref(0)
const expandedCards = ref(new Set())

const newsItems = ref([
  {
    id: 1,
    title: "ቁጥር 3 ስታዲየም ቅርንጫፍ",
    date: "May 14",
    shortContent: "ከነማ ቁጥር 3 ስታዲየም ቅርንጫፍ ለድርጅቱ መድሃኒት የሚያቀርብ የከተማውን የልማት ተጋሪነት ስሜት ባደረባቸው በመድሃኒት አቅራቢ ባለሃብቶች የእድሣት ወጪዉ የተሸፈነው ፋርማሲ የድርጅቱ የቦርድ አባላት እና ባለድርሻ አካላት በተገኙበት ተመርቆ ለአገልግሎት ዋለ",
    fullContent: "በመንግስት የልማት ድርጅት አስተዳደር ባለስልጣን ስር የሚገኘው አንጋፋው የከነማ ፋርማሲዎች ድርጅት ሲቋቋም አብረው አገልግሎት መስጠት ከጀመሩት ቅርንጫፍ መድሃኒት ቤቶች ውስጥ አንዱ የሆነው የስታዲየም ቁጥር 3 ቅርንጫፍ እንደ አዲስ በመታደስ ግንቦት 6/2016 ዓ.ም በልማት አስተዳደር ባለስልጣን ምክትል ስራ አስፈፃሚ በሆኑት በአቶ ባዩ ሽጉጤ ተመርቆ አገልግሎት መስጠት ጀመረ፡፡",
    image: "/src/assets/img/highlight.svg"
  },
  {
    id: 2,
    title: "Infrastructure Development Project",
    date: "May 14",
    shortContent: "Today marks the official launch of our infrastructure development project for #Kenema_Pharmacies_Enterprise (G+2) at Nifas Silk Lafto, Addis Ababa, Ethiopia! The construction is scheduled to be completed within 2 months, bringing to life a new Center of Excellence in Community Pharmacy Services.",
    fullContent: "With a strong commitment to healthcare access, innovation, and excellence, Kenema Pharmacies is proud to take this important step forward. We build the future of pharmacy—together! This will be the first of its kind—offering comprehensive and essential pharmacy services tailored to meet community health needs.",
    image: "/src/assets/img/SeifeEscabator.jpg"
  },
  {
    id: 3,
    title: "የሰንሰለት ፕሮጀክት አጋር ድርጅቶች ከነማን ጎበኙ።",
    date: "April 20",
    shortContent: "በአዲስ አበባ ዩኒቨርሲቲ የፋርማሲ ት/ቤት መሪነት የጤና አቅርቦት ሰንሰለት አስተዳደር እና ትምህርት ላይ የአቅም ግንባታ ስራ እየሰራ ያለው የሰንሰለት ፕሮጀክት ባለፉት 6 ወራት ከ120 በላይ ለሚሆኑ የከነማ ፋርማሲ ባለሙያዎች የአቅም ግንባታ ስልጠና የሰጠ ሲሆን፣",
    fullContent: "የከነማን የአቅርቦት ስርአት እና አገልግሎት ለማሻሻል በሚረዱ ተከታታይ የአቅም ግንባታ ስራዎችና መሰል ጉዳዮች ላይ የኢትርፕራይዙ ዋና ስራ አስኪያጅ ሀና ልካስ ሰፊ ውይይት አድርገዋል። የሰንሰለት አጋር ድርጅቶችም በቀጣይ ከነማን በአቅም ግንባታና መሰል ጉዳዮች በተከታታይ ለማገዝ ቃል ገብተዋል።",
    image: "/src/assets/img/hightight2.svg"
  }
])

const totalSlides = computed(() => newsItems.value.length)

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % totalSlides.value
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? totalSlides.value - 1 : currentSlide.value - 1
}

const goToSlide = (index) => {
  currentSlide.value = index
}

const toggleReadMore = (cardId) => {
  if (expandedCards.value.has(cardId)) {
    expandedCards.value.delete(cardId)
  } else {
    expandedCards.value.add(cardId)
  }
}

const isExpanded = (cardId) => {
  return expandedCards.value.has(cardId)
}
</script>

<template>
  <div class="bg-[#FFFAF5] w-full py-16">
    <div id="news" class="max-w-6xl mx-auto px-4">
      <!-- Title -->
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-gray-900">Latest Highlights</h2>
      </div>

      <!-- Carousel Container -->
      <div class="relative">
        <!-- Navigation Buttons -->
        <button
          @click="prevSlide"
          class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-colors duration-200"
          :disabled="totalSlides <= 1"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>

        <button
          @click="nextSlide"
          class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-colors duration-200"
          :disabled="totalSlides <= 1"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>

        <!-- Slides Container -->
        <div class="overflow-hidden mx-12">
          <div
            class="flex transition-transform duration-500 ease-in-out"
            :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
          >
            <!-- News Cards -->
            <div
              v-for="item in newsItems"
              :key="item.id"
              class="w-full flex-shrink-0 px-4"
            >
              <div class="bg-[#FFF5EC] rounded-lg p-6 shadow-lg h-full flex flex-col">
                <!-- Header -->
                <div class="flex justify-between items-start mb-4">
                  <h3 class="text-lg font-bold text-gray-900 flex-1 pr-4">{{ item.title }}</h3>
                  <span class="text-sm text-gray-600 whitespace-nowrap">{{ item.date }}</span>
                </div>

                <!-- Content -->
                <div class="flex-1 flex flex-col">
                  <!-- Short Content -->
                  <p class="text-justify text-gray-700 mb-4 leading-relaxed">
                    {{ item.shortContent }}
                  </p>

                  <!-- Image -->
                  <div class="mb-4 flex justify-center">
                    <img
                      :src="item.image"
                      :alt="item.title"
                      class="max-w-full h-auto rounded-lg shadow-sm"
                    />
                  </div>

                  <!-- Expandable Full Content -->
                  <div
                    v-if="isExpanded(item.id)"
                    class="mb-4 transition-all duration-300 ease-in-out"
                  >
                    <p class="text-justify text-gray-700 leading-relaxed">
                      {{ item.fullContent }}
                    </p>
                  </div>
                </div>

                <!-- Read More Button -->
                <div class="flex justify-end mt-auto pt-4">
                  <button
                    @click="toggleReadMore(item.id)"
                    class="bg-[#F18A2D] hover:bg-orange-600 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  >
                    {{ isExpanded(item.id) ? 'Read less' : 'Read more' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Dots Indicator -->
        <div class="flex justify-center mt-8 space-x-2">
          <button
            v-for="(item, index) in newsItems"
            :key="index"
            @click="goToSlide(index)"
            class="w-3 h-3 rounded-full transition-colors duration-200"
            :class="currentSlide === index ? 'bg-orange-500' : 'bg-gray-300 hover:bg-gray-400'"
          ></button>
        </div>
      </div>
    </div>
  </div>
</template>